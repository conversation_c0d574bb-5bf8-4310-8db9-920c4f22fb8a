import { NextRequest, NextResponse } from 'next/server';
import { plaidClient } from '@/lib/plaid';
import { createSupabaseServerClient } from '@/lib/supabase/server';

interface ExchangeTokenRequest {
  public_token: string;
  metadata: {
    institution: {
      institution_id: string;
      name: string;
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: ExchangeTokenRequest = await request.json();
    const { public_token, metadata } = body;

    if (!public_token || !metadata?.institution) {
      return NextResponse.json(
        { error: 'Missing required fields: public_token and metadata.institution' },
        { status: 400 }
      );
    }

    // Create Supabase client and get authenticated user
    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Exchange public token for access token using Plaid
    const exchangeResponse = await plaidClient.itemPublicTokenExchange({
      public_token,
    });

    const { access_token, item_id } = exchangeResponse.data;

    // Store credentials in the financial_accounts table
    // Note: Since the current schema doesn't have an access_token column,
    // we'll store it in the plaid_metadata JSONB field
    const { error: insertError } = await supabase.from('financial_accounts').insert({
      user_id: user.id,
      plaid_item_id: item_id,
      plaid_account_id: `temp_${item_id}`, // Temporary placeholder - will be updated when accounts are fetched
      account_name: `${metadata.institution.name} Account`,
      account_type: 'other', // Will be updated when actual account details are fetched
      institution_name: metadata.institution.name,
      institution_id: metadata.institution.institution_id,
      plaid_metadata: {
        access_token, // Store access token in metadata since no dedicated column exists
        item_id,
        institution: metadata.institution,
        exchange_timestamp: new Date().toISOString(),
      },
    });

    if (insertError) {
      console.error('Database insertion error:', insertError);
      return NextResponse.json({ error: 'Failed to store account credentials' }, { status: 500 });
    }

    return NextResponse.json({ ok: true });
  } catch (error) {
    console.error('Error exchanging public token:', error);

    // Handle specific Plaid errors
    if (error && typeof error === 'object' && 'response' in error) {
      const plaidError = error as { response?: { data?: { error_message?: string } } };
      return NextResponse.json(
        {
          error: `Plaid API error: ${plaidError.response?.data?.error_message || 'Unknown error'}`,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({ error: 'Failed to exchange public token' }, { status: 500 });
  }
}
