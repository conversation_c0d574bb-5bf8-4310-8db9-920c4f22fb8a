'use client';

import React, { useState, useEffect } from 'react';
import {
  usePlaidLink,
  PlaidLinkOnSuccess,
  PlaidLinkOnExit,
  PlaidLinkOnSuccessMetadata,
} from 'react-plaid-link';
import { Button } from '@/components/ui/button';

interface PlaidLinkProps {
  userId: string;
}

interface LinkTokenResponse {
  link_token: string;
}

export default function PlaidLink({ userId }: PlaidLinkProps) {
  const [linkToken, setLinkToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch link token when component mounts
  useEffect(() => {
    const fetchLinkToken = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/plaid/create-link-token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId }),
        });

        if (!response.ok) {
          throw new Error(`Failed to create link token: ${response.statusText}`);
        }

        const data: LinkTokenResponse = await response.json();
        setLinkToken(data.link_token);
        console.log('Link token created successfully');
      } catch (error) {
        console.error('Error fetching link token:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLinkToken();
  }, [userId]);

  // Handle successful Plaid Link flow
  const onSuccess: PlaidLinkOnSuccess = async (
    public_token: string,
    metadata: PlaidLinkOnSuccessMetadata
  ) => {
    try {
      console.log('Plaid Link successful, exchanging public token...');

      const response = await fetch('/api/plaid/exchange-public-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          public_token,
          metadata,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to exchange public token: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Public token exchanged successfully:', result);
    } catch (error) {
      console.error('Error exchanging public token:', error);
    }
  };

  // Handle Plaid Link errors
  const onExit: PlaidLinkOnExit = (err, metadata) => {
    if (err) {
      console.error('Plaid Link error:', err);
    }
    console.log('Plaid Link exit metadata:', metadata);
  };

  // Configure Plaid Link
  const config = {
    token: linkToken,
    onSuccess,
    onExit,
  };

  const { open, ready } = usePlaidLink(config);

  // Handle button click
  const handleConnectAccount = () => {
    if (ready && linkToken) {
      open();
    }
  };

  return (
    <Button
      onClick={handleConnectAccount}
      disabled={!ready || !linkToken || isLoading}
      className='w-full'
    >
      {isLoading ? 'Loading...' : 'Connect a bank account'}
    </Button>
  );
}
