import { NextRequest, NextResponse } from 'next/server';
import { plaidClient } from '@/lib/plaid';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { TransactionsSyncRequest } from 'plaid';

interface SyncTransactionsRequest {
  item_id: string;
}

interface PlaidMetadata {
  access_token: string;
  cursor?: string;
  [key: string]: unknown;
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const body: SyncTransactionsRequest = await request.json();
    const { item_id } = body;

    if (!item_id) {
      return NextResponse.json(
        { error: 'Missing required field: item_id' },
        { status: 400 }
      );
    }

    // Create Supabase client and get authenticated user
    const supabase = await createSupabaseServerClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch the financial account record to get access_token and cursor
    const { data: accountData, error: accountError } = await supabase
      .from('financial_accounts')
      .select('id, plaid_metadata')
      .eq('user_id', user.id)
      .eq('plaid_item_id', item_id)
      .single();

    if (accountError || !accountData) {
      return NextResponse.json(
        { error: 'Financial account not found for this item_id' },
        { status: 404 }
      );
    }

    const plaidMetadata = accountData.plaid_metadata as PlaidMetadata;
    const access_token = plaidMetadata.access_token;
    const cursor = plaidMetadata.cursor;

    if (!access_token) {
      return NextResponse.json(
        { error: 'Access token not found in account metadata' },
        { status: 500 }
      );
    }

    // Fetch transactions from Plaid using transactionsSync
    const syncRequest: TransactionsSyncRequest = {
      access_token,
    };

    if (cursor) {
      syncRequest.cursor = cursor;
    }

    const syncResponse = await plaidClient.transactionsSync(syncRequest);
    const { added, modified, removed, next_cursor } = syncResponse.data;

    // Process added and modified transactions
    const transactionsToUpsert = [...added, ...modified];
    
    for (const transaction of transactionsToUpsert) {
      // Map Plaid transaction to our database schema
      const transactionData = {
        user_id: user.id,
        account_id: accountData.id,
        plaid_transaction_id: transaction.transaction_id,
        amount: Math.abs(transaction.amount), // Plaid amounts are negative for debits
        currency_code: transaction.iso_currency_code || 'USD',
        transaction_date: transaction.date,
        authorized_date: transaction.authorized_date || null,
        posted_date: transaction.date, // Use date as posted_date if no specific posted date
        merchant_name: transaction.merchant_name || null,
        description: transaction.name,
        plaid_category: transaction.category || null,
        plaid_category_detailed: transaction.category ? transaction.category.join(' > ') : null,
        account_owner: transaction.account_owner || null,
        transaction_type: transaction.transaction_type || null,
        transaction_code: transaction.transaction_code || null,
        location: transaction.location || null,
        is_pending: transaction.pending || false,
        status: transaction.pending ? 'pending' : 'posted',
        plaid_metadata: {
          original_description: transaction.original_description,
          datetime: transaction.datetime,
          authorized_datetime: transaction.authorized_datetime,
          payment_meta: transaction.payment_meta,
          personal_finance_category: transaction.personal_finance_category,
        },
        updated_at: new Date().toISOString(),
      };

      // Upsert transaction (insert or update if exists)
      const { error: upsertError } = await supabase
        .from('transactions')
        .upsert(transactionData, {
          onConflict: 'user_id,plaid_transaction_id',
          ignoreDuplicates: false,
        });

      if (upsertError) {
        console.error('Error upserting transaction:', upsertError);
        return NextResponse.json(
          { error: `Failed to upsert transaction: ${upsertError.message}` },
          { status: 500 }
        );
      }
    }

    // Process removed transactions
    for (const removedTransactionId of removed) {
      const { error: removeError } = await supabase
        .from('transactions')
        .update({ status: 'removed', updated_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('plaid_transaction_id', removedTransactionId.transaction_id);

      if (removeError) {
        console.error('Error marking transaction as removed:', removeError);
        return NextResponse.json(
          { error: `Failed to mark transaction as removed: ${removeError.message}` },
          { status: 500 }
        );
      }
    }

    // Update the cursor in the financial account's plaid_metadata
    const updatedMetadata = {
      ...plaidMetadata,
      cursor: next_cursor,
      last_sync: new Date().toISOString(),
    };

    const { error: updateError } = await supabase
      .from('financial_accounts')
      .update({
        plaid_metadata: updatedMetadata,
        last_synced_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', accountData.id);

    if (updateError) {
      console.error('Error updating cursor:', updateError);
      return NextResponse.json(
        { error: `Failed to update sync cursor: ${updateError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      summary: {
        added: added.length,
        modified: modified.length,
        removed: removed.length,
        next_cursor,
      },
    });
  } catch (error) {
    console.error('Error syncing transactions:', error);

    // Handle specific Plaid errors
    if (error && typeof error === 'object' && 'response' in error) {
      const plaidError = error as { response?: { data?: { error_message?: string; error_code?: string } } };
      return NextResponse.json(
        {
          error: `Plaid API error: ${plaidError.response?.data?.error_message || 'Unknown error'}`,
          error_code: plaidError.response?.data?.error_code,
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to sync transactions' },
      { status: 500 }
    );
  }
}