import { NextResponse } from 'next/server';
import { plaidClient } from '@/lib/plaid';
import { Products, CountryCode } from 'plaid';

export async function POST() {
  try {
    const createTokenResponse = await plaidClient.linkTokenCreate({
      user: {
        client_user_id: 'user-id', // TODO: Replace with actual authenticated user ID from session
      },
      client_name: 'NAVsync',
      products: [Products.Auth, Products.Transactions],
      country_codes: [CountryCode.Us],
      language: 'en',
    });

    const link_token = createTokenResponse.data.link_token;

    return NextResponse.json({ link_token });
  } catch (error) {
    console.error('Error creating Plaid link token:', error);
    return NextResponse.json({ error: 'Failed to create link token' }, { status: 500 });
  }
}
