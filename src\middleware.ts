// src/middleware.ts
import { createServerClient } from '@/lib/supabase/server';
import { NextResponse, type NextRequest } from 'next/server';

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) => request.cookies.set(name, value));
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // IMPORTANT: DO NOT REMOVE auth.getUser()
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // --- START: Add new root path handling logic here ---
  if (request.nextUrl.pathname === '/') {
    const url = request.nextUrl.clone();
    if (user) {
      // User is logged in
      url.pathname = '/dashboard';
    } else {
      // User is not logged in
      url.pathname = '/login';
    }
    return NextResponse.redirect(url);
  }
  // --- END: Add new root path handling logic here ---

  if (
    !user &&
    !request.nextUrl.pathname.startsWith('/login') && // Allow access to /login
    !request.nextUrl.pathname.startsWith('/auth') && // Allow access to /auth (e.g. /auth/callback)
    !request.nextUrl.pathname.startsWith('/reset-password') && // Allow access to /reset-password
    !request.nextUrl.pathname.startsWith('/signup') // Allow access to /signup
    // Add any other public routes here, e.g. marketing pages
  ) {
    const url = request.nextUrl.clone();
    url.pathname = '/login';
    return NextResponse.redirect(url);
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  return supabaseResponse;
}

export const config = {
  matcher: [
    '/dashboard',
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api (API routes, if you have any that should be public or handle auth differently)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|api|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
