'use client';

import React, { useEffect, useState } from 'react';
import AccountCard from './AccountCard';

interface Account {
  id: string;
  account_name: string;
  institution_name: string;
  current_balance: number | null;
  last_synced_at: string | null;
  plaid_item_id: string;
}

export default function AccountsList() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchAccounts() {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/plaid/get-accounts');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch accounts');
        }
        const data = await response.json();
        setAccounts(data.accounts);
      } catch (err: unknown) {
        if (err instanceof Error) {
          setError(err.message);
        } else {
          setError('Unknown error');
        }
      } finally {
        setLoading(false);
      }
    }

    fetchAccounts();
  }, []);

  if (loading) {
    return <div>Loading accounts...</div>;
  }

  if (error) {
    return <div className="text-red-600">Error: {error}</div>;
  }

  if (accounts.length === 0) {
    return <div>No connected financial accounts found.</div>;
  }

  return (
    <div className="flex flex-col items-center w-full">
      {accounts.map((account) => (
        <AccountCard key={account.id} account={account} />
      ))}
    </div>
  );
}